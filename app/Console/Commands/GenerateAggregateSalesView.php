<?php

namespace App\Console\Commands;

use App\Jobs\GenerateAggregateSalesByOrderView;
use Illuminate\Console\Command;

class GenerateAggregateSalesView extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'reports:generate-aggregate-sales-view';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate the aggregate sales by order database view';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Generating aggregate sales by order view...');
        
        try {
            GenerateAggregateSalesByOrderView::dispatch();
            $this->info('✅ Aggregate sales view generated successfully!');
        } catch (\Exception $e) {
            $this->error('❌ Failed to generate aggregate sales view: ' . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
