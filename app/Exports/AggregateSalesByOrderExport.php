<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class AggregateSalesByOrderExport implements FromCollection, WithHeadings, WithMapping
{
    use Exportable;

    public function __construct(
        protected Collection $data,
    ) {}

    public function collection(): Collection
    {
        return $this->data;
    }

    public function headings(): array
    {
        return [
            'Order ID',
            'Order Type',
            'Order Item ID',
            'Product Type',
            'Billable',
            'Product Barcode',
            'Product ID',
            'SKU',
            'Title',
            'Unit of Issue',
            'Quantity',
            'Sort ID',
            'Packing Group',
            'Accounting Class ID',
            'Pounds per Unit',
            'Total Pounds',
            'Retail Price per Unit',
            'Total Retail Price',
            'Billed Price per Unit',
            'Total Billed Price',
            'Discount per Unit',
            'Total Discount',
            'Confirmation Date',
            'Deadline Date',
            'Pack Date',
            'Payment Date',
            'Delivery Date',
            'Location Name',
            'Location ID',
            'Schedule Name',
            'Schedule ID',
            'Customer ID',
            'Customer First Name',
            'Customer Last Name',
            'Customer Phone',
            'Customer Email',
            'Shipping Street',
            'Shipping Street 2',
            'Shipping City',
            'Shipping State',
            'Shipping ZIP',
            'Shipping Country',
            'Customer Order Count',
            'Profile Notes',
            'Customer Notes',
            'Private Notes',
            'Invoice Notes',
            'Payment Notes',
        ];
    }

    public function map($row): array
    {
        return [
            $row->order_id,
            $row->order_type,
            $row->order_item_id,
            $row->product_type,
            $row->billable,
            $row->product_barcode,
            $row->product_id,
            $row->sku,
            $row->title,
            $row->unit_of_issue,
            $row->quantity,
            $row->sort_id,
            $row->packing_group,
            $row->accounting_class_id,
            $row->pounds_per_unit,
            $row->total_pounds,
            $row->retail_price_per_unit > 0 ? money($row->retail_price_per_unit) : '0',
            $row->total_retail_price > 0 ? money($row->total_retail_price) : '0',
            $row->billed_price_per_unit > 0 ? money($row->billed_price_per_unit) : '0',
            $row->total_billed_price > 0 ? money($row->total_billed_price) : '0',
            $row->discount_per_unit > 0 ? money($row->discount_per_unit) : '0',
            $row->total_discount > 0 ? money($row->total_discount) : '0',
            $row->confirmation_date,
            $row->deadline_date,
            $row->pack_date,
            $row->payment_date,
            $row->delivery_date,
            $row->location_name,
            $row->location_id,
            $row->schedule_name,
            $row->schedule_id,
            $row->customer_id,
            $row->customer_first_name,
            $row->customer_last_name,
            $row->customer_phone,
            $row->customer_email,
            $row->shipping_street,
            $row->shipping_street_2,
            $row->shipping_city,
            $row->shipping_state,
            $row->shipping_zip,
            $row->shipping_country,
            $row->customer_order_count,
            $row->profile_notes,
            $row->customer_notes,
            $row->private_notes,
            $row->invoice_notes,
            $row->payment_notes,
        ];
    }
}
