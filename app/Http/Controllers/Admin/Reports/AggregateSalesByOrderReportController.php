<?php

namespace App\Http\Controllers\Admin\Reports;

use App\Exports\AggregateSalesByOrderExport;
use App\Http\Controllers\Controller;
use App\Repositories\Reports\AggregateSalesByOrderReport;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class AggregateSalesByOrderReportController extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'confirmation_date_start' => 'nullable|date',
            'confirmation_date_end' => 'nullable|date|after_or_equal:confirmation_date_start',
            'delivery_date_start' => 'nullable|date',
            'delivery_date_end' => 'nullable|date|after_or_equal:delivery_date_start',
            'order_type' => 'nullable|in:Subscription,One Time',
            'product_type' => 'nullable|in:Bundle,Standard',
            'location_id' => 'nullable|array',
            'location_id.*' => 'integer',
            'schedule_id' => 'nullable|array',
            'schedule_id.*' => 'integer',
            'packing_group' => 'nullable|array',
            'accounting_class_id' => 'nullable|array',
            'customer_id' => 'nullable|array',
            'customer_id.*' => 'integer',
            'billable' => 'nullable|in:Y,N',
            'group_by' => 'nullable|in:order_type,product_type,packing_group,location_name,schedule_name,accounting_class_id,billable',
            'export' => 'nullable|boolean',
        ]);

        $filters = $request->only([
            'confirmation_date_start',
            'confirmation_date_end',
            'delivery_date_start',
            'delivery_date_end',
            'order_type',
            'product_type',
            'location_id',
            'schedule_id',
            'packing_group',
            'accounting_class_id',
            'customer_id',
            'billable',
        ]);

        // Remove null values from filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null && $value !== '';
        });

        $report = new AggregateSalesByOrderReport();

        // Handle export request
        if ($request->has('export')) {
            $data = $report->handle($filters);
            
            $filename = 'aggregate_sales_by_order_' . now()->format('Y_m_d_H_i_s') . '.csv';
            
            return (new AggregateSalesByOrderExport($data))
                ->download($filename, Excel::CSV);
        }

        // Handle grouped data request
        if ($request->has('group_by')) {
            $groupedData = $report->getGroupedData($request->get('group_by'), $filters);
            $summary = $report->getSummary($filters);
            
            return response()->json([
                'grouped_data' => $groupedData,
                'summary' => $summary,
                'filters_applied' => $filters,
            ]);
        }

        // Regular report view
        $data = $request->has('limit') 
            ? $report->handle($filters)->take($request->get('limit', 100))
            : $report->handle($filters);
            
        $summary = $report->getSummary($filters);

        return response()->json([
            'data' => $data,
            'summary' => $summary,
            'filters_applied' => $filters,
            'total_records' => $data->count(),
        ]);
    }

    /**
     * Get available filter options
     */
    public function filterOptions()
    {
        $report = new AggregateSalesByOrderReport();
        
        // Get distinct values for filter dropdowns
        $locations = $report->baseQuery()
            ->select('location_id', 'location_name')
            ->distinct()
            ->orderBy('location_name')
            ->get();

        $schedules = $report->baseQuery()
            ->select('schedule_id', 'schedule_name')
            ->distinct()
            ->orderBy('schedule_name')
            ->get();

        $packingGroups = $report->baseQuery()
            ->select('packing_group')
            ->distinct()
            ->orderBy('packing_group')
            ->pluck('packing_group');

        $accountingClasses = $report->baseQuery()
            ->select('accounting_class_id')
            ->distinct()
            ->whereNotNull('accounting_class_id')
            ->where('accounting_class_id', '!=', '')
            ->orderBy('accounting_class_id')
            ->pluck('accounting_class_id');

        return response()->json([
            'locations' => $locations,
            'schedules' => $schedules,
            'packing_groups' => $packingGroups,
            'accounting_classes' => $accountingClasses,
            'order_types' => ['Subscription', 'One Time'],
            'product_types' => ['Bundle', 'Standard'],
            'billable_options' => ['Y', 'N'],
        ]);
    }
}
