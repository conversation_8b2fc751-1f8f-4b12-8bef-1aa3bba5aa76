<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class GenerateAggregateSalesByOrderView implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle()
    {
        DB::statement('CREATE OR REPLACE VIEW aggregate_sales_by_order AS
            SELECT
                o.id as order_id,
                CASE WHEN o.blueprint_id IS NOT NULL THEN "Subscription" ELSE "One Time" END AS order_type,
                oi.id as order_item_id,
                CASE WHEN p.is_bundle = 1 THEN "Bundle" ELSE "Standard" END as product_type,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN "N" 
                    ELSE "Y" 
                END as billable,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_product.barcode 
                    ELSE p.barcode 
                END as product_barcode,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp.product_id 
                    ELSE oi.product_id 
                END AS product_id,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_product.sku 
                    ELSE p.sku 
                END as sku,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_product.title 
                    ELSE oi.title 
                END as title,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_product.unit_of_issue 
                    ELSE p.unit_of_issue 
                END as unit_of_issue,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN (oi.fulfilled_qty * bp.qty) 
                    ELSE oi.fulfilled_qty 
                END as quantity,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_product.custom_sort 
                    ELSE p.custom_sort 
                END as sort_id,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_pg.title 
                    ELSE pg.title 
                END as packing_group,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN bp_product.accounting_class 
                    ELSE p.accounting_class 
                END as accounting_class_id,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE oi.weight 
                END as pounds_per_unit,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE (oi.weight * oi.fulfilled_qty) 
                END as total_pounds,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE oi.store_price 
                END as retail_price_per_unit,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE (oi.store_price * oi.fulfilled_qty) 
                END as total_retail_price,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE oi.unit_price 
                END as billed_price_per_unit,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE (oi.unit_price * oi.fulfilled_qty) 
                END as total_billed_price,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE (oi.store_price - oi.unit_price) 
                END as discount_per_unit,
                CASE 
                    WHEN bp.product_id IS NOT NULL THEN 0 
                    ELSE ((oi.store_price * oi.fulfilled_qty) - (oi.unit_price * oi.fulfilled_qty)) 
                END as total_discount,
                o.confirmed_date as confirmation_date,
                o.deadline_date as deadline_date,
                o.pack_deadline_at as pack_date,
                o.payment_date as payment_date,
                o.pickup_date as delivery_date,
                pi.title as location_name,
                pi.id as location_id,
                s.title as schedule_name,
                s.id as schedule_id,
                o.customer_id as customer_id,
                o.customer_first_name as customer_first_name,
                o.customer_last_name as customer_last_name,
                o.customer_phone as customer_phone,
                o.customer_email as customer_email,
                o.shipping_street as shipping_street,
                o.shipping_street_2 as shipping_street_2,
                o.shipping_city as shipping_city,
                o.shipping_state as shipping_state,
                o.shipping_zip as shipping_zip,
                "USA" as shipping_country,
                u.order_count as customer_order_count,
                u.notes as profile_notes,
                o.customer_notes as customer_notes,
                o.packing_notes as private_notes,
                o.invoice_notes as invoice_notes,
                o.payment_notes as payment_notes
            FROM order_items oi
            INNER JOIN orders o ON oi.order_id = o.id
            INNER JOIN users u ON o.customer_id = u.id
            INNER JOIN products p ON oi.product_id = p.id 
            INNER JOIN packing_groups pg ON p.inventory_type = pg.id
            INNER JOIN pickups pi ON o.pickup_id = pi.id
            INNER JOIN schedules s ON o.schedule_id = s.id
            LEFT JOIN bundle_product bp ON oi.product_id = bp.bundle_id
            LEFT JOIN products bp_product ON bp.product_id = bp_product.id
            LEFT JOIN packing_groups bp_pg ON bp_product.inventory_type = bp_pg.id
            WHERE o.confirmed = 1
                AND o.canceled = 0
                AND o.confirmed_date >= "2025-01-01"
            ORDER BY o.id, oi.id;
        ');
    }
}
