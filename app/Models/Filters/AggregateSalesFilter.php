<?php

namespace App\Models\Filters;

use EloquentFilter\ModelFilter;

class AggregateSalesFilter extends ModelFilter
{
    /**
     * Related Models that have ModelFilters as well as the method on the ModelFilter
     * As [relationMethod => [input_key1, input_key2]].
     *
     * @var array
     */
    public $relations = [];

    /**
     * Filter by confirmation date range
     */
    public function confirmationDateStart($date)
    {
        return $this->where('confirmation_date', '>=', $date);
    }

    public function confirmationDateEnd($date)
    {
        return $this->where('confirmation_date', '<=', $date);
    }

    /**
     * Filter by delivery date range
     */
    public function deliveryDateStart($date)
    {
        return $this->where('delivery_date', '>=', $date);
    }

    public function deliveryDateEnd($date)
    {
        return $this->where('delivery_date', '<=', $date);
    }

    /**
     * Filter by order type
     */
    public function orderType($type)
    {
        return $this->where('order_type', $type);
    }

    /**
     * Filter by product type
     */
    public function productType($type)
    {
        return $this->where('product_type', $type);
    }

    /**
     * Filter by location
     */
    public function locationId($locationIds)
    {
        if (is_array($locationIds)) {
            return $this->whereIn('location_id', $locationIds);
        }
        
        return $this->where('location_id', $locationIds);
    }

    /**
     * Filter by schedule
     */
    public function scheduleId($scheduleIds)
    {
        if (is_array($scheduleIds)) {
            return $this->whereIn('schedule_id', $scheduleIds);
        }
        
        return $this->where('schedule_id', $scheduleIds);
    }

    /**
     * Filter by packing group
     */
    public function packingGroup($groups)
    {
        if (is_array($groups)) {
            return $this->whereIn('packing_group', $groups);
        }
        
        return $this->where('packing_group', $groups);
    }

    /**
     * Filter by accounting class
     */
    public function accountingClassId($classes)
    {
        if (is_array($classes)) {
            return $this->whereIn('accounting_class_id', $classes);
        }
        
        return $this->where('accounting_class_id', $classes);
    }

    /**
     * Filter by customer
     */
    public function customerId($customerIds)
    {
        if (is_array($customerIds)) {
            return $this->whereIn('customer_id', $customerIds);
        }
        
        return $this->where('customer_id', $customerIds);
    }

    /**
     * Filter by billable status
     */
    public function billable($billable)
    {
        return $this->where('billable', $billable);
    }

    /**
     * Filter by product SKU
     */
    public function sku($sku)
    {
        return $this->where('sku', 'LIKE', "%{$sku}%");
    }

    /**
     * Filter by product title
     */
    public function title($title)
    {
        return $this->where('title', 'LIKE', "%{$title}%");
    }

    /**
     * Filter by customer name
     */
    public function customerName($name)
    {
        return $this->where(function ($query) use ($name) {
            $query->where('customer_first_name', 'LIKE', "%{$name}%")
                  ->orWhere('customer_last_name', 'LIKE', "%{$name}%");
        });
    }

    /**
     * Filter by minimum quantity
     */
    public function minQuantity($quantity)
    {
        return $this->where('quantity', '>=', $quantity);
    }

    /**
     * Filter by maximum quantity
     */
    public function maxQuantity($quantity)
    {
        return $this->where('quantity', '<=', $quantity);
    }

    /**
     * Filter by minimum total billed price
     */
    public function minTotalBilledPrice($price)
    {
        return $this->where('total_billed_price', '>=', $price);
    }

    /**
     * Filter by maximum total billed price
     */
    public function maxTotalBilledPrice($price)
    {
        return $this->where('total_billed_price', '<=', $price);
    }
}
