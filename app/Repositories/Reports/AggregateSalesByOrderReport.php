<?php

namespace App\Repositories\Reports;

use App\Models\Filters\AggregateSalesFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AggregateSalesByOrderReport
{
    /**
     * Get aggregate sales data with optional filters
     *
     * @param array $filters
     * @return Collection
     */
    public function handle(array $filters = []): Collection
    {
        $query = $this->baseQuery();
        
        if (!empty($filters)) {
            $query = $this->applyFilters($query, $filters);
        }
        
        return $query->get();
    }

    /**
     * Get the base query for aggregate sales
     *
     * @return Builder
     */
    protected function baseQuery(): Builder
    {
        return DB::table('aggregate_sales_by_order')
            ->select([
                'order_id',
                'order_type',
                'order_item_id',
                'product_type',
                'billable',
                'product_barcode',
                'product_id',
                'sku',
                'title',
                'unit_of_issue',
                'quantity',
                'sort_id',
                'packing_group',
                'accounting_class_id',
                'pounds_per_unit',
                'total_pounds',
                'retail_price_per_unit',
                'total_retail_price',
                'billed_price_per_unit',
                'total_billed_price',
                'discount_per_unit',
                'total_discount',
                'confirmation_date',
                'deadline_date',
                'pack_date',
                'payment_date',
                'delivery_date',
                'location_name',
                'location_id',
                'schedule_name',
                'schedule_id',
                'customer_id',
                'customer_first_name',
                'customer_last_name',
                'customer_phone',
                'customer_email',
                'shipping_street',
                'shipping_street_2',
                'shipping_city',
                'shipping_state',
                'shipping_zip',
                'shipping_country',
                'customer_order_count',
                'profile_notes',
                'customer_notes',
                'private_notes',
                'invoice_notes',
                'payment_notes'
            ]);
    }

    /**
     * Apply filters to the query
     *
     * @param Builder $query
     * @param array $filters
     * @return Builder
     */
    protected function applyFilters(Builder $query, array $filters): Builder
    {
        // Date range filters
        if (isset($filters['confirmation_date_start'])) {
            $query->where('confirmation_date', '>=', $filters['confirmation_date_start']);
        }
        
        if (isset($filters['confirmation_date_end'])) {
            $query->where('confirmation_date', '<=', $filters['confirmation_date_end']);
        }
        
        if (isset($filters['delivery_date_start'])) {
            $query->where('delivery_date', '>=', $filters['delivery_date_start']);
        }
        
        if (isset($filters['delivery_date_end'])) {
            $query->where('delivery_date', '<=', $filters['delivery_date_end']);
        }

        // Order type filter
        if (isset($filters['order_type'])) {
            $query->where('order_type', $filters['order_type']);
        }

        // Product type filter
        if (isset($filters['product_type'])) {
            $query->where('product_type', $filters['product_type']);
        }

        // Location filter
        if (isset($filters['location_id'])) {
            if (is_array($filters['location_id'])) {
                $query->whereIn('location_id', $filters['location_id']);
            } else {
                $query->where('location_id', $filters['location_id']);
            }
        }

        // Schedule filter
        if (isset($filters['schedule_id'])) {
            if (is_array($filters['schedule_id'])) {
                $query->whereIn('schedule_id', $filters['schedule_id']);
            } else {
                $query->where('schedule_id', $filters['schedule_id']);
            }
        }

        // Packing group filter
        if (isset($filters['packing_group'])) {
            if (is_array($filters['packing_group'])) {
                $query->whereIn('packing_group', $filters['packing_group']);
            } else {
                $query->where('packing_group', $filters['packing_group']);
            }
        }

        // Accounting class filter
        if (isset($filters['accounting_class_id'])) {
            if (is_array($filters['accounting_class_id'])) {
                $query->whereIn('accounting_class_id', $filters['accounting_class_id']);
            } else {
                $query->where('accounting_class_id', $filters['accounting_class_id']);
            }
        }

        // Customer filter
        if (isset($filters['customer_id'])) {
            if (is_array($filters['customer_id'])) {
                $query->whereIn('customer_id', $filters['customer_id']);
            } else {
                $query->where('customer_id', $filters['customer_id']);
            }
        }

        // Billable filter
        if (isset($filters['billable'])) {
            $query->where('billable', $filters['billable']);
        }

        return $query;
    }

    /**
     * Get summary statistics for the filtered data
     *
     * @param array $filters
     * @return array
     */
    public function getSummary(array $filters = []): array
    {
        $query = $this->baseQuery();
        
        if (!empty($filters)) {
            $query = $this->applyFilters($query, $filters);
        }

        $summary = $query
            ->selectRaw('
                COUNT(DISTINCT order_id) as total_orders,
                COUNT(*) as total_line_items,
                SUM(quantity) as total_quantity,
                SUM(total_pounds) as total_weight,
                SUM(total_retail_price) as total_retail_value,
                SUM(total_billed_price) as total_billed_value,
                SUM(total_discount) as total_discount_value,
                COUNT(DISTINCT customer_id) as unique_customers
            ')
            ->first();

        return [
            'total_orders' => $summary->total_orders ?? 0,
            'total_line_items' => $summary->total_line_items ?? 0,
            'total_quantity' => $summary->total_quantity ?? 0,
            'total_weight' => round($summary->total_weight ?? 0, 2),
            'total_retail_value' => $summary->total_retail_value ?? 0,
            'total_billed_value' => $summary->total_billed_value ?? 0,
            'total_discount_value' => $summary->total_discount_value ?? 0,
            'unique_customers' => $summary->unique_customers ?? 0,
            'average_order_value' => $summary->total_orders > 0 
                ? round(($summary->total_billed_value ?? 0) / $summary->total_orders, 2) 
                : 0,
        ];
    }

    /**
     * Get data grouped by a specific field
     *
     * @param string $groupBy
     * @param array $filters
     * @return Collection
     */
    public function getGroupedData(string $groupBy, array $filters = []): Collection
    {
        $query = $this->baseQuery();
        
        if (!empty($filters)) {
            $query = $this->applyFilters($query, $filters);
        }

        $validGroupByFields = [
            'order_type', 'product_type', 'packing_group', 'location_name', 
            'schedule_name', 'accounting_class_id', 'billable'
        ];

        if (!in_array($groupBy, $validGroupByFields)) {
            throw new \InvalidArgumentException("Invalid groupBy field: {$groupBy}");
        }

        return $query
            ->selectRaw("
                {$groupBy},
                COUNT(DISTINCT order_id) as order_count,
                COUNT(*) as line_item_count,
                SUM(quantity) as total_quantity,
                SUM(total_pounds) as total_weight,
                SUM(total_retail_price) as total_retail_value,
                SUM(total_billed_price) as total_billed_value,
                SUM(total_discount) as total_discount_value
            ")
            ->groupBy($groupBy)
            ->orderBy($groupBy)
            ->get();
    }
}
