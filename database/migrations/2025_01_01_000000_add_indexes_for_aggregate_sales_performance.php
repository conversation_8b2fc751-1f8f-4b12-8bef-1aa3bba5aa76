<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add composite indexes for better query performance
        Schema::table('orders', function (Blueprint $table) {
            // Index for the main WHERE conditions in the aggregate sales query
            $table->index(['confirmed', 'canceled', 'confirmed_date'], 'orders_aggregate_sales_filter');
            
            // Index for date range queries
            $table->index(['confirmed_date', 'pickup_date'], 'orders_date_range');
            
            // Index for blueprint_id (subscription detection)
            $table->index('blueprint_id', 'orders_blueprint_id');
        });

        Schema::table('order_items', function (Blueprint $table) {
            // Composite index for order_items joins
            $table->index(['order_id', 'product_id'], 'order_items_order_product');
            
            // Index for fulfilled_qty queries
            $table->index('fulfilled_qty', 'order_items_fulfilled_qty');
        });

        Schema::table('products', function (Blueprint $table) {
            // Index for bundle detection and inventory_type joins
            $table->index(['is_bundle', 'inventory_type'], 'products_bundle_inventory');
            
            // Index for accounting_class filtering
            $table->index('accounting_class', 'products_accounting_class');
        });

        Schema::table('bundle_product', function (Blueprint $table) {
            // Composite index for bundle product lookups
            $table->index(['bundle_id', 'product_id'], 'bundle_product_lookup');
        });

        Schema::table('users', function (Blueprint $table) {
            // Index for order_count if not already present
            if (!Schema::hasIndex('users', 'users_order_count')) {
                $table->index('order_count', 'users_order_count');
            }
        });

        Schema::table('pickups', function (Blueprint $table) {
            // Index for pickup title and schedule_id if not already present
            if (!Schema::hasIndex('pickups', 'pickups_title_schedule')) {
                $table->index(['title', 'schedule_id'], 'pickups_title_schedule');
            }
        });

        Schema::table('schedules', function (Blueprint $table) {
            // Index for schedule title if not already present
            if (!Schema::hasIndex('schedules', 'schedules_title')) {
                $table->index('title', 'schedules_title');
            }
        });

        Schema::table('packing_groups', function (Blueprint $table) {
            // Index for packing group title if not already present
            if (!Schema::hasIndex('packing_groups', 'packing_groups_title')) {
                $table->index('title', 'packing_groups_title');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('orders_aggregate_sales_filter');
            $table->dropIndex('orders_date_range');
            $table->dropIndex('orders_blueprint_id');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex('order_items_order_product');
            $table->dropIndex('order_items_fulfilled_qty');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('products_bundle_inventory');
            $table->dropIndex('products_accounting_class');
        });

        Schema::table('bundle_product', function (Blueprint $table) {
            $table->dropIndex('bundle_product_lookup');
        });

        Schema::table('users', function (Blueprint $table) {
            if (Schema::hasIndex('users', 'users_order_count')) {
                $table->dropIndex('users_order_count');
            }
        });

        Schema::table('pickups', function (Blueprint $table) {
            if (Schema::hasIndex('pickups', 'pickups_title_schedule')) {
                $table->dropIndex('pickups_title_schedule');
            }
        });

        Schema::table('schedules', function (Blueprint $table) {
            if (Schema::hasIndex('schedules', 'schedules_title')) {
                $table->dropIndex('schedules_title');
            }
        });

        Schema::table('packing_groups', function (Blueprint $table) {
            if (Schema::hasIndex('packing_groups', 'packing_groups_title')) {
                $table->dropIndex('packing_groups_title');
            }
        });
    }
};
