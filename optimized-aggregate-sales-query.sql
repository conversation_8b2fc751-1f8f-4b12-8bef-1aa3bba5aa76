-- Optimized Aggregate Sales by Order Query
-- This maintains the same row count as the original UNION query but with better performance

SELECT
    o.id as order_id,
    CASE WHEN o.blueprint_id IS NOT NULL THEN 'Subscription' ELSE 'One Time' END AS order_type,
    oi.id as order_item_id,
    CASE WHEN p.is_bundle = 1 THEN 'Bundle' ELSE 'Standard' END as product_type,
    'Y' as billable,
    p.barcode as product_barcode,
    oi.product_id as product_id,
    p.sku as sku,
    oi.title as title,
    p.unit_of_issue as unit_of_issue,
    oi.fulfilled_qty as quantity,
    p.custom_sort as sort_id,
    pg.title as packing_group,
    p.accounting_class as accounting_class_id,
    oi.weight as pounds_per_unit,
    oi.weight * oi.fulfilled_qty as total_pounds,
    oi.store_price as retail_price_per_unit,
    oi.store_price * oi.fulfilled_qty as total_retail_price,
    oi.unit_price as billed_price_per_unit,
    oi.unit_price * oi.fulfilled_qty as total_billed_price,
    oi.store_price - oi.unit_price as discount_per_unit,
    (oi.store_price * oi.fulfilled_qty) - (oi.unit_price * oi.fulfilled_qty) as total_discount, 
    o.confirmed_date as confirmation_date,
    o.deadline_date as deadline_date,
    o.pack_deadline_at as pack_date,
    o.payment_date as payment_date,
    o.pickup_date as delivery_date,
    pi.title as location_name,
    pi.id as location_id,
    s.title as schedule_name,
    s.id as schedule_id,
    o.customer_id as customer_id,
    o.customer_first_name as customer_first_name,
    o.customer_last_name as customer_last_name,
    o.customer_phone as customer_phone,
    o.customer_email as customer_email,
    o.shipping_street as shipping_street,
    o.shipping_street_2 as shipping_street_2,
    o.shipping_city as shipping_city,
    o.shipping_state as shipping_state,
    o.shipping_zip as shipping_zip,
    'USA' as shipping_country,
    u.order_count as customer_order_count,
    u.notes as profile_notes,
    o.customer_notes as customer_notes,
    o.packing_notes as private_notes,
    o.invoice_notes as invoice_notes,
    o.payment_notes as payment_notes
FROM order_items oi
INNER JOIN orders o ON oi.order_id = o.id
INNER JOIN users u ON o.customer_id = u.id
INNER JOIN products p ON oi.product_id = p.id 
INNER JOIN packing_groups pg ON p.inventory_type = pg.id
INNER JOIN pickups pi ON o.pickup_id = pi.id
INNER JOIN schedules s ON o.schedule_id = s.id
WHERE o.confirmed = 1
    AND o.canceled = 0
    AND o.confirmed_date >= '2025-01-01'

UNION ALL

SELECT
    o.id as order_id,
    CASE WHEN o.blueprint_id IS NOT NULL THEN 'Subscription' ELSE 'One Time' END AS order_type,
    oi.id as order_item_id,
    CASE WHEN p.is_bundle = 1 THEN 'Bundle' ELSE 'Standard' END as product_type,
    'N' as billable,
    bp_product.barcode as product_barcode,
    bp.product_id as product_id,
    bp_product.sku as sku,
    bp_product.title as title,
    bp_product.unit_of_issue as unit_of_issue,
    oi.fulfilled_qty * bp.qty as quantity,
    bp_product.custom_sort as sort_id,
    bp_pg.title as packing_group,
    bp_product.accounting_class as accounting_class_id,
    0 as pounds_per_unit,
    0 as total_pounds,
    0 as retail_price_per_unit,
    0 as total_retail_price,
    0 as billed_price_per_unit,
    0 as total_billed_price,
    0 as discount_per_unit,
    0 as total_discount, 
    o.confirmed_date as confirmation_date,
    o.deadline_date as deadline_date,
    o.pack_deadline_at as pack_date,
    o.payment_date as payment_date,
    o.pickup_date as delivery_date,
    pi.title as location_name,
    pi.id as location_id,
    s.title as schedule_name,
    s.id as schedule_id,
    o.customer_id as customer_id,
    o.customer_first_name as customer_first_name,
    o.customer_last_name as customer_last_name,
    o.customer_phone as customer_phone,
    o.customer_email as customer_email,
    o.shipping_street as shipping_street,
    o.shipping_street_2 as shipping_street_2,
    o.shipping_city as shipping_city,
    o.shipping_state as shipping_state,
    o.shipping_zip as shipping_zip,
    'USA' as shipping_country,
    u.order_count as customer_order_count,
    u.notes as profile_notes,
    o.customer_notes as customer_notes,
    o.packing_notes as private_notes,
    o.invoice_notes as invoice_notes,
    o.payment_notes as payment_notes
FROM order_items oi
INNER JOIN orders o ON oi.order_id = o.id
INNER JOIN users u ON o.customer_id = u.id
INNER JOIN products p ON oi.product_id = p.id 
INNER JOIN packing_groups pg ON p.inventory_type = pg.id
INNER JOIN pickups pi ON o.pickup_id = pi.id
INNER JOIN schedules s ON o.schedule_id = s.id
INNER JOIN bundle_product bp ON oi.product_id = bp.bundle_id
INNER JOIN products bp_product ON bp.product_id = bp_product.id
INNER JOIN packing_groups bp_pg ON bp_product.inventory_type = bp_pg.id
WHERE o.confirmed = 1
    AND o.canceled = 0
    AND o.confirmed_date >= '2025-01-01'
ORDER BY order_id, order_item_id;
