-- Performance indexes for aggregate sales query
-- Run these to optimize query performance

-- Orders table indexes
CREATE INDEX IF NOT EXISTS idx_orders_aggregate_filter ON orders (confirmed, canceled, confirmed_date);
CREATE INDEX IF NOT EXISTS idx_orders_blueprint ON orders (blueprint_id);

-- Order items table indexes  
CREATE INDEX IF NOT EXISTS idx_order_items_order_product ON order_items (order_id, product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_fulfilled_qty ON order_items (fulfilled_qty);

-- Products table indexes
CREATE INDEX IF NOT EXISTS idx_products_bundle_inventory ON products (is_bundle, inventory_type);
CREATE INDEX IF NOT EXISTS idx_products_accounting_class ON products (accounting_class);

-- Bundle product table indexes
CREATE INDEX IF NOT EXISTS idx_bundle_product_lookup ON bundle_product (bundle_id, product_id);

-- Users table index
CREATE INDEX IF NOT EXISTS idx_users_order_count ON users (order_count);

-- Pickups and schedules indexes (if not already present)
CREATE INDEX IF NOT EXISTS idx_pickups_title ON pickups (title);
CREATE INDEX IF NOT EXISTS idx_schedules_title ON schedules (title);
CREATE INDEX IF NOT EXISTS idx_packing_groups_title ON packing_groups (title);
