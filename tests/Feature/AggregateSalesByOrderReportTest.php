<?php

namespace Tests\Feature;

use App\Jobs\GenerateAggregateSalesByOrderView;
use App\Repositories\Reports\AggregateSalesByOrderReport;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class AggregateSalesByOrderReportTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Generate the database view for testing
        GenerateAggregateSalesByOrderView::dispatch();
    }

    /** @test */
    public function it_can_generate_the_database_view()
    {
        // Check if the view exists
        $viewExists = DB::select("SHOW TABLES LIKE 'aggregate_sales_by_order'");
        
        $this->assertNotEmpty($viewExists, 'The aggregate_sales_by_order view should exist');
    }

    /** @test */
    public function it_can_fetch_report_data()
    {
        $report = new AggregateSalesByOrderReport();
        
        // This should not throw an exception even with empty data
        $data = $report->handle();
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $data);
    }

    /** @test */
    public function it_can_apply_filters()
    {
        $report = new AggregateSalesByOrderReport();
        
        $filters = [
            'order_type' => 'Subscription',
            'confirmation_date_start' => '2025-01-01',
        ];
        
        // This should not throw an exception
        $data = $report->handle($filters);
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $data);
    }

    /** @test */
    public function it_can_generate_summary_statistics()
    {
        $report = new AggregateSalesByOrderReport();
        
        $summary = $report->getSummary();
        
        $this->assertIsArray($summary);
        $this->assertArrayHasKey('total_orders', $summary);
        $this->assertArrayHasKey('total_line_items', $summary);
        $this->assertArrayHasKey('total_quantity', $summary);
        $this->assertArrayHasKey('total_weight', $summary);
        $this->assertArrayHasKey('total_retail_value', $summary);
        $this->assertArrayHasKey('total_billed_value', $summary);
        $this->assertArrayHasKey('total_discount_value', $summary);
        $this->assertArrayHasKey('unique_customers', $summary);
        $this->assertArrayHasKey('average_order_value', $summary);
    }

    /** @test */
    public function it_can_generate_grouped_data()
    {
        $report = new AggregateSalesByOrderReport();
        
        $groupedData = $report->getGroupedData('order_type');
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $groupedData);
    }

    /** @test */
    public function it_validates_group_by_fields()
    {
        $report = new AggregateSalesByOrderReport();
        
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid groupBy field: invalid_field');
        
        $report->getGroupedData('invalid_field');
    }

    /** @test */
    public function it_can_handle_array_filters()
    {
        $report = new AggregateSalesByOrderReport();
        
        $filters = [
            'location_id' => [1, 2, 3],
            'schedule_id' => [1, 2],
        ];
        
        // This should not throw an exception
        $data = $report->handle($filters);
        
        $this->assertInstanceOf(\Illuminate\Support\Collection::class, $data);
    }
}
