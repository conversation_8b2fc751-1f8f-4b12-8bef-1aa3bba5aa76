-- Simple verification script to compare row counts
-- Run each query separately and compare the counts

-- 1. Count from your original query
SELECT COUNT(*) as original_count FROM (
    -- Paste your original query here
    SELECT
        o.id as order_id,
        CASE WHEN o.blueprint_id is NOT null then 'Subscription' else 'One Time' End AS order_type,
        oi.id as order_item_id,
        CASE WHEN p.is_bundle = 1 THEN 'Bundle' ELSE 'Standard' end as product_type,
        'Y' as billable,
        p.barcode as product_barcode,
        oi.product_id as product_id,
        p.sku as sku,
        oi.title as title,
        p.unit_of_issue as unit_of_issue,
        oi.fulfilled_qty as quantity,
        p.custom_sort as sort_id,
        pg.title as packing_group,
        p.accounting_class as accounting_class_id,
        oi.weight as pounds_per_unit,
        oi.weight * oi.fulfilled_qty as total_pounds,
        oi.store_price as retail_price_per_unit,
        oi.store_price  * oi.fulfilled_qty as total_retail_price,
        oi.unit_price as billed_price_per_unit,
        oi.unit_price * oi.fulfilled_qty as total_billed_price,
        oi.store_price - oi.unit_price as discount_per_unit,
        (oi.store_price  * oi.fulfilled_qty) - (oi.unit_price * oi.fulfilled_qty) as total_discount, 
        o.confirmed_date as confirmation_date,
        o.deadline_date as deadline_date,
        o.pack_deadline_at as pack_date,
        o.payment_date as payment_date,
        o.pickup_date as delivery_date,
        pi.title as location_name,
        pi.id as location_id,
        s.title as schedule_name,
        s.id as schedule_id,
        o.customer_id as customer_id,
        o.customer_first_name as customer_first_name,
        o.customer_last_name as customer_last_name,
        o.customer_phone as customer_phone,
        o.customer_email as customer_email,
        o.shipping_street as shipping_street,
        o.shipping_street_2 as shipping_street_2,
        o.shipping_city as shipping_city,
        o.shipping_state as shipping_state,
        o.shipping_zip as shipping_zip,
        'USA' as shipping_country,
        '12345' as customer_order_count,
        u.notes as profile_notes,
        o.customer_notes as customer_notes,
        o.packing_notes as private_notes,
        o.invoice_notes as invoice_notes,
        o.payment_notes as payment_notes
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    JOIN users u ON o.customer_id = u.id
    join products p ON oi.product_id = p.id 
    join packing_groups pg on p.inventory_type = pg.id
    join pickups pi on o.pickup_id = pi.id
    JOIN schedules s ON o.schedule_id = s.id
    WHERE o.confirmed = 1
    And o.canceled = 0
    And o.confirmed_date >= '2025-01-01'
    UNION 
    SELECT
        o.id as order_id,
        CASE WHEN o.blueprint_id is NOT null then 'Subscription' else 'One Time' End AS order_type,
        oi.id as order_item_id,
        CASE WHEN p.is_bundle = 1 THEN 'Bundle' ELSE 'Standard' end as product_type,
        'N' as billable,
        p.barcode as product_barcode,
        CASE WHEN bp.product_id IS NOT NULL THEN bp.product_id ELSE oi.product_id END AS product_id,
        p.sku as sku,
        p.title as title,
        p.unit_of_issue as unit_of_issue,
        oi.fulfilled_qty * bp.qty as quantity,
        p.custom_sort as sort_id,
        pg.title as packing_group,
        p.accounting_class as accounting_class_id,
        0 as pounds_per_unit,
        0 as total_pounds,
        0 as retail_price_per_unit,
        0 as total_retail_price,
        0 as billed_price_per_unit,
        0 as total_billed_price,
        0 as discount_per_unit,
        0 as total_discount, 
        o.confirmed_date as confirmation_date,
        o.deadline_date as deadline_date,
        o.pack_deadline_at as pack_date,
        o.payment_date as payment_date,
        o.pickup_date as delivery_date,
        pi.title as location_name,
        pi.id as location_id,
        s.title as schedule_name,
        s.id as schedule_id,
        o.customer_id as customer_id,
        o.customer_first_name as customer_first_name,
        o.customer_last_name as customer_last_name,
        o.customer_phone as customer_phone,
        o.customer_email as customer_email,
        o.shipping_street as shipping_street,
        o.shipping_street_2 as shipping_street_2,
        o.shipping_city as shipping_city,
        o.shipping_state as shipping_state,
        o.shipping_zip as shipping_zip,
        'USA' as shipping_country,
        '12345' as customer_order_count,
        u.notes as profile_notes,
        o.customer_notes as customer_notes,
        o.packing_notes as private_notes,
        o.invoice_notes as invoice_notes,
        o.payment_notes as payment_notes
    FROM order_items oi
    JOIN orders o ON oi.order_id = o.id
    JOIN users u ON o.customer_id = u.id
    join products p ON oi.product_id = p.id 
    join packing_groups pg on p.inventory_type = pg.id
    join pickups pi on o.pickup_id = pi.id
    JOIN schedules s ON o.schedule_id = s.id
    JOIN bundle_product bp ON oi.product_id = bp.bundle_id
    WHERE o.confirmed = 1
    And o.canceled = 0
    And o.confirmed_date >= '2025-01-01'
    Order BY order_id, order_item_id
) as original_query;

-- 2. Count from optimized query (copy from optimized-aggregate-sales-query.sql)
-- Should return the same count as above

-- 3. Debug: Check if you have bundle products
SELECT 
    'Bundle products in system' as check_type,
    COUNT(*) as count
FROM products 
WHERE is_bundle = 1

UNION ALL

SELECT 
    'Bundle relationships' as check_type,
    COUNT(*) as count
FROM bundle_product

UNION ALL

SELECT 
    'Orders with bundles in date range' as check_type,
    COUNT(DISTINCT o.id) as count
FROM orders o
JOIN order_items oi ON o.id = oi.order_id
JOIN products p ON oi.product_id = p.id
WHERE o.confirmed = 1
    AND o.canceled = 0
    AND o.confirmed_date >= '2025-01-01'
    AND p.is_bundle = 1;
